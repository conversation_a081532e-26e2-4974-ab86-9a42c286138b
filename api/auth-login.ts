import { type VercelRequest, type VercelResponse } from '@vercel/node';

export default async function handler(req: VercelRequest, res: VercelResponse) {
  const clientId = process.env.STRAVA_CLIENT_ID;
  const redirectUri = process.env.STRAVA_REDIRECT_URI || `${(req.headers['x-forwarded-proto'] as string) || 'https'}://${(req.headers['x-forwarded-host'] as string) || req.headers.host}/api/auth-callback`;
  const scope = process.env.STRAVA_SCOPE || 'read,activity:read_all,profile:read_all';

  if (!clientId) {
    return res.status(500).json({ error: 'Missing STRAVA_CLIENT_ID env var' });
  }

  const authUrl = new URL('https://www.strava.com/oauth/authorize');
  authUrl.searchParams.set('client_id', clientId);
  authUrl.searchParams.set('redirect_uri', redirectUri);
  authUrl.searchParams.set('response_type', 'code');
  authUrl.searchParams.set('approval_prompt', 'auto');
  authUrl.searchParams.set('scope', scope);

  res.status(302).setHeader('Location', authUrl.toString()).end();
}
