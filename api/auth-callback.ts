import { type VercelRequest, type VercelResponse } from '@vercel/node';
import axios from 'axios';

export default async function handler(req: VercelRequest, res: VercelResponse) {
  const { code, error } = req.query as { code?: string; error?: string };

  if (error) {
    return res.status(400).send('Authorization failed.');
  }

  const clientId = process.env.STRAVA_CLIENT_ID;
  const clientSecret = process.env.STRAVA_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    return res.status(500).json({ error: 'Missing STRAVA_CLIENT_ID or STRAVA_CLIENT_SECRET env var' });
  }

  if (!code) {
    return res.status(400).json({ error: 'Missing authorization code' });
  }

  try {
    const body = new URLSearchParams();
    body.set('client_id', clientId);
    body.set('client_secret', clientSecret);
    body.set('code', code);
    body.set('grant_type', 'authorization_code');
    const tokenRes = await axios.post('https://www.strava.com/oauth/token', body.toString(), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });

    const { access_token, refresh_token, expires_at } = tokenRes.data;

    const cookieBase = 'Path=/; HttpOnly; SameSite=Lax; Secure';
    res.setHeader('Set-Cookie', [
      `strava_access_token=${access_token}; ${cookieBase}; Max-Age=${60 * 60 * 6}`,
      `strava_refresh_token=${refresh_token}; ${cookieBase}; Max-Age=${60 * 60 * 24 * 365}`,
      `strava_expires_at=${expires_at}; Path=/; SameSite=Lax; Secure; Max-Age=${60 * 60 * 24 * 365}`,
    ]);

    res.status(302).setHeader('Location', '/dashboard').end();
  } catch (e: any) {
    const details = e.response?.data || e.message;
    console.error('Strava token exchange failed:', details);
    res.status(500).json({ error: 'Token exchange failed', details });
  }
}


