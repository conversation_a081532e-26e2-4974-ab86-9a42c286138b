import { type VercelRequest, type VercelResponse } from '@vercel/node';

export default async function handler(req: VercelRequest, res: VercelResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  const cookieBase = 'Path=/; HttpOnly; SameSite=Lax; Secure; Max-Age=0';
  res.setHeader('Set-Cookie', [
    `strava_access_token=; ${cookieBase}`,
    `strava_refresh_token=; ${cookieBase}`,
    `strava_expires_at=; Path=/; SameSite=Lax; Secure; Max-Age=0`,
  ]);
  res.status(200).json({ ok: true });
}


