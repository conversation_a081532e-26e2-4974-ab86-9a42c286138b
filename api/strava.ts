import { type VercelRequest, type VercelResponse } from '@vercel/node';
import axios from 'axios';

const stravaApi = axios.create({
  baseURL: 'https://www.strava.com/api/v3',
});

function parseCookies(cookieHeader: string | undefined): Record<string, string> {
  const result: Record<string, string> = {};
  if (!cookieHeader) return result;
  const parts = cookieHeader.split(';');
  for (const part of parts) {
    const [rawKey, ...rest] = part.trim().split('=');
    const key = decodeURIComponent(rawKey);
    const value = decodeURIComponent(rest.join('='));
    if (key) result[key] = value;
  }
  return result;
}

function buildBaseUrl(req: VercelRequest): string {
  const proto = (req.headers['x-forwarded-proto'] as string) || 'https';
  const host = (req.headers['x-forwarded-host'] as string) || req.headers.host || 'localhost';
  return `${proto}://${host}`;
}

async function refreshAccessTokenIfNeeded(req: VercelRequest, res: VercelResponse, cookies: Record<string, string>): Promise<string | null> {
  const clientId = process.env.STRAVA_CLIENT_ID;
  const clientSecret = process.env.STRAVA_CLIENT_SECRET;
  const refreshToken = cookies['strava_refresh_token'];
  const expiresAt = Number(cookies['strava_expires_at'] || '0');
  const accessToken = cookies['strava_access_token'];

  const now = Math.floor(Date.now() / 1000);
  const isExpired = !expiresAt || expiresAt <= now + 60; // refresh if expiring within 60s

  if (accessToken && !isExpired) {
    return accessToken;
  }

  if (!clientId || !clientSecret || !refreshToken) {
    return null;
  }

  try {
    const body = new URLSearchParams();
    body.set('client_id', clientId);
    body.set('client_secret', clientSecret);
    body.set('grant_type', 'refresh_token');
    body.set('refresh_token', refreshToken);
    const tokenRes = await axios.post('https://www.strava.com/oauth/token', body.toString(), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
    const { access_token, refresh_token, expires_at } = tokenRes.data;
    const cookieBase = 'Path=/; HttpOnly; SameSite=Lax; Secure';
    res.setHeader('Set-Cookie', [
      `strava_access_token=${access_token}; ${cookieBase}; Max-Age=${60 * 60 * 6}`,
      `strava_refresh_token=${refresh_token}; ${cookieBase}; Max-Age=${60 * 60 * 24 * 365}`,
      `strava_expires_at=${expires_at}; Path=/; SameSite=Lax; Secure; Max-Age=${60 * 60 * 24 * 365}`,
    ]);
    return access_token;
  } catch (e: any) {
    console.error('Failed to refresh Strava token:', e.response?.data || e.message);
    return null;
  }
}

export default async function handler(
  req: VercelRequest,
  res: VercelResponse,
) {
  const cookies = parseCookies(req.headers.cookie);

  let accessToken: string | null = null;

  // Prefer Authorization header when provided (e.g., manual/dev usage)
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    accessToken = authHeader.split(' ')[1];
  } else {
    // Fallback to cookie-based session
    accessToken = await refreshAccessTokenIfNeeded(req, res, cookies);
  }

  if (!accessToken) {
    return res.status(401).json({ error: 'Unauthorized. Please connect your Strava account.' });
  }

  try {
    // 1. Get athlete profile
    const athleteRes = await stravaApi.get('/athlete', {
      headers: { Authorization: `Bearer ${accessToken}` },
    });
    const athlete = athleteRes.data;

    // 2. Get athlete stats
    const statsRes = await stravaApi.get(`/athletes/${athlete.id}/stats`, {
      headers: { Authorization: `Bearer ${accessToken}` },
    });
    const stats = statsRes.data;

    // 3. Get athlete activities
    const activitiesRes = await stravaApi.get('/athlete/activities', {
      headers: { Authorization: `Bearer ${accessToken}` },
      params: { per_page: 50 },
    });
    const activities = activitiesRes.data || [];

    // 4. Build profile object
    const profile = {
      id: athlete.id.toString(),
      name: `${athlete.firstname} ${athlete.lastname}`,
      city: athlete.city,
      country: athlete.country,
      totals: {
        activities:
          (stats.all_run_totals?.count || 0) +
          (stats.all_ride_totals?.count || 0) +
          (stats.all_swim_totals?.count || 0),
        distance_km:
          ((stats.all_run_totals?.distance || 0) +
            (stats.all_ride_totals?.distance || 0) +
            (stats.all_swim_totals?.distance || 0)) /
          1000,
        time_hours:
          ((stats.all_run_totals?.moving_time || 0) +
            (stats.all_ride_totals?.moving_time || 0) +
            (stats.all_swim_totals?.moving_time || 0)) /
          3600,
        elevation_m:
          (stats.all_run_totals?.elevation_gain || 0) +
          (stats.all_ride_totals?.elevation_gain || 0),
      },
      gear: (athlete.bikes || [])
        .map((b: any) => ({
          id: b.id,
          name: b.name,
          type: 'Bike',
          distance_km: b.distance / 1000,
        }))
        .concat(
          (athlete.shoes || []).map((s: any) => ({
            id: s.id,
            name: s.name,
            type: 'Shoes',
            distance_km: s.distance / 1000,
          })),
        ),
      activities: activities.map((a: any) => ({
        id: a.id.toString(),
        name: a.name,
        type: a.type,
        distance_km: a.distance / 1000,
        moving_time_min: a.moving_time / 60,
        elevation_gain_m: a.total_elevation_gain,
        date: a.start_date,
        avg_speed_kmh: a.average_speed * 3.6,
        gear_id: a.gear_id,
      })),
    };

    res.status(200).json(profile);
  } catch (error: any) {
    const status = error.response?.status || 500;
    const details = error.response?.data || error.message;
    console.error('Error fetching data from Strava:', details);
    res.status(status).json({ error: 'Failed to fetch data from Strava', details, hint: 'Ensure tokens are valid and scopes include read,activity:read_all,profile:read_all' });
  }
}
