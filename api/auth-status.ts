import { type VercelRequest, type VercelResponse } from '@vercel/node';
import axios from 'axios';

// --- Start of duplicated code from api/strava.ts ---

function parseCookies(cookieHeader: string | undefined): Record<string, string> {
  const result: Record<string, string> = {};
  if (!cookieHeader) return result;
  const parts = cookieHeader.split(';');
  for (const part of parts) {
    const [rawKey, ...rest] = part.trim().split('=');
    const key = decodeURIComponent(rawKey);
    const value = decodeURIComponent(rest.join('='));
    if (key) result[key] = value;
  }
  return result;
}

async function refreshAccessTokenIfNeeded(req: VercelRequest, res: VercelResponse, cookies: Record<string, string>): Promise<string | null> {
  const clientId = process.env.STRAVA_CLIENT_ID;
  const clientSecret = process.env.STRAVA_CLIENT_SECRET;
  const refreshToken = cookies['strava_refresh_token'];
  const expiresAt = Number(cookies['strava_expires_at'] || '0');
  const accessToken = cookies['strava_access_token'];

  const now = Math.floor(Date.now() / 1000);
  const isExpired = !expiresAt || expiresAt <= now + 60; // refresh if expiring within 60s

  if (accessToken && !isExpired) {
    return accessToken;
  }

  if (!clientId || !clientSecret || !refreshToken) {
    return null;
  }

  try {
    const body = new URLSearchParams();
    body.set('client_id', clientId);
    body.set('client_secret', clientSecret);
    body.set('grant_type', 'refresh_token');
    body.set('refresh_token', refreshToken);
    const tokenRes = await axios.post('https://www.strava.com/oauth/token', body.toString(), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
    const { access_token, refresh_token, expires_at } = tokenRes.data;
    const cookieBase = 'Path=/; HttpOnly; SameSite=Lax; Secure';
    res.setHeader('Set-Cookie', [
      `strava_access_token=${access_token}; ${cookieBase}; Max-Age=${60 * 60 * 6}`,
      `strava_refresh_token=${refresh_token}; ${cookieBase}; Max-Age=${60 * 60 * 24 * 365}`,
      `strava_expires_at=${expires_at}; Path=/; SameSite=Lax; Secure; Max-Age=${60 * 60 * 24 * 365}`,
    ]);
    return access_token;
  } catch (e: any) {
    console.error('Failed to refresh Strava token:', e.response?.data || e.message);
    return null;
  }
}

// --- End of duplicated code ---


export default async function handler(
  req: VercelRequest,
  res: VercelResponse,
) {
  const cookies = parseCookies(req.headers.cookie);
  const accessToken = await refreshAccessTokenIfNeeded(req, res, cookies);

  if (!accessToken) {
    // It's important to send a 401 status for unauthenticated,
    // but the client query will treat this as an error state.
    return res.status(401).json({ loggedIn: false });
  }

  // If we have a token, the user is considered logged in.
  res.status(200).json({ loggedIn: true });
}
