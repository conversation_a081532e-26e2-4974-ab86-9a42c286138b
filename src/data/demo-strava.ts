export type Gear = {
  id: string;
  name: string;
  type: 'Bike' | 'Shoes';
  distance_km: number;
};

export type Activity = {
  id: string;
  name: string;
  type: 'Run' | 'Ride' | 'Swim' | 'Hike' | 'Walk';
  distance_km: number;
  moving_time_min: number;
  elevation_gain_m: number;
  date: string; // ISO date
  avg_speed_kmh: number;
  gear_id?: string;
};

export type AthleteProfile = {
  id: string;
  name: string;
  city?: string;
  country?: string;
  totals: {
    activities: number;
    distance_km: number;
    time_hours: number;
    elevation_m: number;
  };
  gear: Gear[];
  activities: Activity[];
};

export const demoProfile: AthleteProfile = {
  id: '123456',
  name: 'Alex Rider',
  city: 'San Francisco',
  country: 'USA',
  totals: {
    activities: 326,
    distance_km: 4123,
    time_hours: 356,
    elevation_m: 61234,
  },
  gear: [
    { id: 'g1', name: 'Canyon Endurace CF', type: 'Bike', distance_km: 2680 },
    { id: 'g2', name: 'Nike Pegasus 40', type: 'Shoes', distance_km: 540 },
    { id: 'g3', name: 'Specialized Diverge', type: 'Bike', distance_km: 903 },
  ],
  activities: [
    { id: 'a1', name: 'Morning Ride', type: 'Ride', distance_km: 42.5, moving_time_min: 95, elevation_gain_m: 620, date: '2025-07-20', avg_speed_kmh: 26.8, gear_id: 'g1' },
    { id: 'a2', name: 'Evening Run', type: 'Run', distance_km: 8.2, moving_time_min: 43, elevation_gain_m: 110, date: '2025-07-19', avg_speed_kmh: 11.4, gear_id: 'g2' },
    { id: 'a3', name: 'Lunchtime Walk', type: 'Walk', distance_km: 3.1, moving_time_min: 36, elevation_gain_m: 20, date: '2025-07-18', avg_speed_kmh: 5.2 },
    { id: 'a4', name: 'Trail Run', type: 'Run', distance_km: 15.0, moving_time_min: 92, elevation_gain_m: 680, date: '2025-07-16', avg_speed_kmh: 9.8, gear_id: 'g2' },
    { id: 'a5', name: 'Long Ride', type: 'Ride', distance_km: 95.3, moving_time_min: 230, elevation_gain_m: 1450, date: '2025-07-13', avg_speed_kmh: 24.9, gear_id: 'g3' },
    { id: 'a6', name: 'Open Water Swim', type: 'Swim', distance_km: 2.6, moving_time_min: 54, elevation_gain_m: 0, date: '2025-07-12', avg_speed_kmh: 2.9 },
    { id: 'a7', name: 'Hike with friends', type: 'Hike', distance_km: 12.4, moving_time_min: 210, elevation_gain_m: 820, date: '2025-07-06', avg_speed_kmh: 3.5 },
  ],
};
