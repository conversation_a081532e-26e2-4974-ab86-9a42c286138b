export function formatKm(km: number) {
  return `${km.toFixed(1)} km`;
}
export function formatHours(hours: number) {
  return `${hours.toFixed(1)} h`;
}
export function formatElevation(m: number) {
  return `${Math.round(m).toLocaleString()} m`;
}
export function formatPaceFromSpeedKmh(speed: number) {
  if (!speed) return '-';
  const paceMinPerKm = 60 / speed;
  const m = Math.floor(paceMinPerKm);
  const s = Math.round((paceMinPerKm - m) * 60)
    .toString()
    .padStart(2, '0');
  return `${m}:${s}/km`;
}
export function niceDate(iso: string) {
  return new Date(iso).toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
}
