import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

export const PageSection = ({
  children,
  className,
}: React.PropsWithChildren<{ className?: string }>) => (
  <section className={cn("container mx-auto px-4", className)}>{children}</section>
);

export const GlassCard = ({ children, className }: React.PropsWithChildren<{ className?: string }>) => (
  <Card className={cn("backdrop-glass shadow-elegant", className)}>
    <CardContent className="p-6">{children}</CardContent>
  </Card>
);
