import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import type { ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";
import { ArrowRight } from "lucide-react";

export function TokenInput({
  onConnect,
  variant = "hero",
  size = "xl",
  inline = false,
  inputClassName,
}: {
  onConnect: (token: string) => void;
  variant?: ButtonProps["variant"];
  size?: ButtonProps["size"];
  inline?: boolean;
  inputClassName?: string;
}) {
  const [token, setToken] = useState("");

  return (
    <div className={inline ? "flex flex-row flex-wrap md:flex-nowrap gap-2 items-center w-full" : "flex flex-col md:flex-row gap-3 md:items-center"}>
      <div className={inline ? "flex-1 min-w-0 sm:min-w-[340px]" : "flex-1 min-w-[260px] space-y-1"}>
        <Input
          value={token}
          onChange={(e) => setToken(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              if (!token) {
                toast({ title: 'Enter a token', description: 'Paste your Strava access token.' });
              } else {
                onConnect(token);
              }
            }
          }}
          placeholder="Paste your Strava access token"
          aria-label="Strava access token"
          type="password"
          className={cn(inline ? "w-full min-w-0" : "md:w-[420px]", inputClassName)}
        />
        {!inline && (
          <p id="token-help" className="text-xs text-muted-foreground">We’ll use this token to fetch your data. It will be stored in your browser.</p>
        )}
      </div>
      <div className={inline ? "" : "flex gap-2"}>
        <Button
          variant={variant}
          size={size}
          className="transition-transform hover:scale-105 shrink-0"
          title="Analyze your profile with the provided token"
          onClick={() => {
            if (!token) {
              toast({ title: 'Enter a token', description: 'Paste your Strava access token' });
              return;
            }
            onConnect(token);
          }}
        >
          Analyze my profile
          <ArrowRight className="ml-1" />
        </Button>
      </div>
    </div>
  );
}
