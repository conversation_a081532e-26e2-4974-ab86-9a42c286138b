import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import type { Activity } from "@/data/demo-strava";
import { niceDate, formatKm, formatPaceFromSpeedKmh } from "@/lib/format";

export function RecentActivitiesTable({ activities }: { activities?: Activity[] }) {
  const list = Array.isArray(activities) ? activities : [];
  const recent = [...list]
    .sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 8);
  return (
    <Card className="shadow-elegant">
      <CardHeader>
        <CardTitle>Recent Activities</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          {recent.length === 0 ? (
            <div className="py-10 text-center text-sm text-muted-foreground">No recent activities.</div>
          ) : (
          <table className="w-full text-sm">
            <thead className="text-muted-foreground">
              <tr className="text-left">
                <th className="py-2">Date</th>
                <th className="py-2">Name</th>
                <th className="py-2">Type</th>
                <th className="py-2">Distance</th>
                <th className="py-2">Pace/Speed</th>
                <th className="py-2">Elev</th>
              </tr>
            </thead>
            <tbody>
              {recent.map(a => (
                <tr key={a.id} className="border-b last:border-b-0">
                  <td className="py-2">{niceDate(a.date)}</td>
                  <td className="py-2 font-medium">{a.name}</td>
                  <td className="py-2">{a.type}</td>
                  <td className="py-2">{formatKm(a.distance_km)}</td>
                  <td className="py-2">{a.type === 'Run' ? formatPaceFromSpeedKmh(a.avg_speed_kmh) : `${a.avg_speed_kmh} km/h`}</td>
                  <td className="py-2">{Math.round(a.elevation_gain_m)} m</td>
                </tr>
              ))}
            </tbody>
          </table>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
