import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import type { Gear, Activity } from "@/data/demo-strava";
import { Bike, Footprints } from "lucide-react";

function distanceByGear(activities: Activity[] = [], gear: Gear[] = []) {
  const totals = new Map<string, number>();
  activities.forEach(a => {
    if (!a.gear_id) return;
    totals.set(a.gear_id, (totals.get(a.gear_id) || 0) + a.distance_km);
  });
  return gear.map(g => ({ ...g, distance_km: Number((totals.get(g.id) || g.distance_km).toFixed(1)) }));
}

export function EquipmentList({ activities, gear }: { activities?: Activity[]; gear?: Gear[] }) {
  const data = distanceByGear(activities || [], gear || []);
  return (
    <Card className="shadow-elegant">
      <CardHeader>
        <CardTitle>Equipment</CardTitle>
      </CardHeader>
      <CardContent>
        {data.length === 0 ? (
          <div className="py-10 text-center text-sm text-muted-foreground">No gear data.</div>
        ) : (
          <ul className="space-y-3">
            {data.map(g => (
              <li key={g.id} className="flex items-center justify-between border-b py-2 last:border-0">
                <div className="flex items-center gap-3">
                  <span className="text-primary">{g.type === 'Bike' ? <Bike /> : <Footprints />}</span>
                  <span className="font-medium">{g.name}</span>
                </div>
                <span className="text-sm text-muted-foreground">{g.distance_km.toLocaleString()} km</span>
              </li>
            ))}
          </ul>
        )}
      </CardContent>
    </Card>
  );
}
