import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Cell } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { Activity } from "@/data/demo-strava";

const COLORS = [
  'hsl(18 98% 50%)',
  'hsl(200 90% 45%)',
  'hsl(140 70% 38%)',
  'hsl(270 70% 50%)',
  'hsl(45 95% 50%)',
];

export function ActivityTypeDistribution({ activities }: { activities?: Activity[] }) {
  const grouped = (activities || []).reduce<Record<string, number>>((acc, a) => {
    acc[a.type] = (acc[a.type] || 0) + a.distance_km;
    return acc;
  }, {});
  const data = Object.entries(grouped).map(([name, value]) => ({ name, value: Number(value.toFixed(1)) }));

  return (
    <Card className="shadow-elegant">
      <CardHeader>
        <CardTitle>Distance by Activity Type</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-72">
          {data.length === 0 ? (
            <div className="h-full flex items-center justify-center text-sm text-muted-foreground">No activity data yet.</div>
          ) : (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={data} dataKey="value" nameKey="name" innerRadius={60} outerRadius={100} paddingAngle={2}>
                {data.map((_, idx) => (
                  <Cell key={`cell-${idx}`} fill={COLORS[idx % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(val: any) => `${val} km`} />
            </PieChart>
          </ResponsiveContainer>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
