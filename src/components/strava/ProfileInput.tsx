import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { toast } from "@/hooks/use-toast";
import { ArrowRight } from "lucide-react";
export function ProfileInput({ onConnect }: { onConnect: (profileUrlOrId: string) => void }) {
  const [value, setValue] = useState("");

  return (
    <div className="flex flex-col md:flex-row gap-3 md:items-center">
      <div className="flex-1 min-w-[260px] space-y-1">
        <Input
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              if (!value) {
                toast({ title: 'Enter a profile', description: 'Paste a Strava profile URL or athlete ID' });
              } else {
                onConnect(value);
              }
            }
          }}
          placeholder="Paste Strava profile URL or athlete ID"
          aria-label="Strava profile"
          aria-describedby="profile-help"
          autoFocus
          className="md:w-[420px]"
        />
        <p id="profile-help" className="text-xs text-muted-foreground">We’ll analyze the URL or ID you paste. Press Enter or click Analyze.</p>
      </div>
      <div className="flex gap-2">
        <Button
          variant="hero"
          size="xl"
          className="transition-transform hover:scale-105"
          title="Analyze the pasted Strava URL or athlete ID"
          onClick={() => {
            if (!value) {
              toast({ title: 'Enter a profile', description: 'Paste a Strava profile URL or athlete ID' });
              return;
            }
            onConnect(value);
          }}
        >
          Analyze profile
          <ArrowRight className="ml-1" />
        </Button>
      </div>
    </div>
  );
}
