import { Card, CardContent } from "@/components/ui/card";
import { Bike, BarChart3, TrendingUp, Timer } from "lucide-react";
import { formatElevation, formatHours, formatKm } from "@/lib/format";

export function SummaryCards({ totals }: { totals?: { activities: number; distance_km: number; time_hours: number; elevation_m: number; } }) {
  if (!totals) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[0,1,2,3].map((i) => (
          <Card key={i} className="shadow-elegant">
            <CardContent className="p-5">
              <div className="h-10 w-full animate-pulse bg-muted rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const items = [
    { label: 'Activities', value: totals.activities.toLocaleString(), icon: BarChart3 },
    { label: 'Distance', value: formatKm(totals.distance_km), icon: TrendingUp },
    { label: 'Time', value: formatHours(totals.time_hours), icon: Timer },
    { label: 'Elevation', value: formatElevation(totals.elevation_m), icon: Bike },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {items.map(({ label, value, icon: Icon }) => (
        <Card key={label} className="shadow-elegant">
          <CardContent className="p-5 flex items-center gap-4">
            <div className="rounded-md bg-primary/10 text-primary p-2"><Icon /></div>
            <div>
              <div className="text-sm text-muted-foreground">{label}</div>
              <div className="text-xl font-semibold">{value}</div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
