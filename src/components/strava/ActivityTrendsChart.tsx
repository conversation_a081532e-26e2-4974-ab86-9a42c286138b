import { Line, <PERSON><PERSON>hart, Responsive<PERSON>ontainer, Tooltip, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid } from 'recharts';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import type { Activity } from "@/data/demo-strava";

function aggregateByDate(activities: Activity[] = []) {
  // Simple 14-day window aggregation by date
  const map = new Map<string, number>();
  activities.forEach(a => {
    const d = new Date(a.date).toISOString().slice(0, 10);
    map.set(d, (map.get(d) || 0) + a.distance_km);
  });
  const days = [...Array(14)].map((_, i) => {
    const d = new Date();
    d.setDate(d.getDate() - (13 - i));
    const key = d.toISOString().slice(0,10);
    return { date: key, distance: Number((map.get(key) || 0).toFixed(1)) };
  });
  return days;
}

export function ActivityTrendsChart({ activities }: { activities?: Activity[] }) {
  const data = aggregateByDate(activities || []);
  return (
    <Card className="shadow-elegant">
      <CardHeader>
        <CardTitle>Daily Distance (last 2 weeks)</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-72">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ left: 8, right: 8, top: 8, bottom: 8 }}>
              <CartesianGrid strokeDasharray="4 4" className="stroke-muted" />
              <XAxis dataKey="date" tickFormatter={(d) => new Date(d).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })} />
              <YAxis width={40} tickFormatter={(v) => `${v}k`} />
              <Tooltip formatter={(val: any) => `${val} km`} labelFormatter={(d) => new Date(d).toLocaleDateString()} />
              <Line type="monotone" dataKey="distance" stroke="hsl(18 98% 50%)" strokeWidth={3} dot={{ r: 2 }} activeDot={{ r: 4 }} />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}
