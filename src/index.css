@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 222.2 84% 4.9%;

		--card: 0 0% 100%;
		--card-foreground: 222.2 84% 4.9%;

		--popover: 0 0% 100%;
		--popover-foreground: 222.2 84% 4.9%;

		--primary: 222.2 47.4% 11.2%;
		--primary-foreground: 210 40% 98%;

		--secondary: 210 40% 96.1%;
		--secondary-foreground: 222.2 47.4% 11.2%;

		--muted: 210 40% 96.1%;
		--muted-foreground: 215.4 16.3% 46.9%;

		--accent: 210 40% 96.1%;
		--accent-foreground: 222.2 47.4% 11.2%;

		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 210 40% 98%;

		--border: 214.3 31.8% 91.4%;
		--input: 214.3 31.8% 91.4%;
		--ring: 222.2 84% 4.9%;

		--radius: 0.5rem;

		--sidebar-background: 0 0% 98%;

		--sidebar-foreground: 240 5.3% 26.1%;

		--sidebar-primary: 240 5.9% 10%;

		--sidebar-primary-foreground: 0 0% 98%;

		--sidebar-accent: 240 4.8% 95.9%;

		--sidebar-accent-foreground: 240 5.9% 10%;

		--sidebar-border: 220 13% 91%;

		--sidebar-ring: 217.2 91.2% 59.8%;
	}

	.dark {
		--background: 222.2 84% 4.9%;
		--foreground: 210 40% 98%;

		--card: 222.2 84% 4.9%;
		--card-foreground: 210 40% 98%;

		--popover: 222.2 84% 4.9%;
		--popover-foreground: 210 40% 98%;

		--primary: 210 40% 98%;
		--primary-foreground: 222.2 47.4% 11.2%;

		--secondary: 217.2 32.6% 17.5%;
		--secondary-foreground: 210 40% 98%;

		--muted: 217.2 32.6% 17.5%;
		--muted-foreground: 215 20.2% 65.1%;

		--accent: 217.2 32.6% 17.5%;
		--accent-foreground: 210 40% 98%;

		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 210 40% 98%;

		--border: 217.2 32.6% 17.5%;
		--input: 217.2 32.6% 17.5%;
		--ring: 212.7 26.8% 83.9%;
		--sidebar-background: 240 5.9% 10%;
		--sidebar-foreground: 240 4.8% 95.9%;
		--sidebar-primary: 224.3 76.3% 48%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 240 3.7% 15.9%;
		--sidebar-accent-foreground: 240 4.8% 95.9%;
		--sidebar-border: 240 3.7% 15.9%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}
}

@layer base {
	* {
		@apply border-border;
	}

	body {
		@apply bg-background text-foreground;
		font-family: 'Manrope', ui-sans-serif, system-ui, -apple-system, Segoe UI,
			Roboto, Helvetica, Arial, 'Apple Color Emoji', 'Segoe UI Emoji';
	}
}

/* Additional theme tokens and utilities (override + extend) */
@layer base {
	:root {
		/* Brand-forward accent inspired by Strava orange */
		--primary: 18 98% 50%; /* Strava-like orange */
		--primary-foreground: 210 40% 98%;

		/* Complementary accents and gradients */
		--accent-strong: 16 96% 56%;
		--primary-glow: 20 92% 62%;
		--gradient-primary: linear-gradient(
			135deg,
			hsl(var(--primary)) 0%,
			hsl(var(--primary-glow)) 100%
		);

		/* Shadows */
		--shadow-elegant: 0 10px 30px -10px hsl(var(--primary) / 0.35);
		--shadow-glow: 0 0 24px hsl(var(--primary-glow) / 0.35);
	}

	.dark {
		--primary: 20 92% 62%;
		--primary-foreground: 222.2 47.4% 11.2%;
		--primary-glow: 18 98% 50%;
		--gradient-primary: linear-gradient(
			135deg,
			hsl(var(--primary)) 0%,
			hsl(var(--primary-glow)) 100%
		);
	}
}

@layer utilities {
	.bg-gradient-primary {
		background-image: var(--gradient-primary);
	}
	.shadow-elegant {
		box-shadow: var(--shadow-elegant);
	}
	.shadow-glow {
		box-shadow: var(--shadow-glow);
	}
	.backdrop-glass {
		@apply bg-background/60 backdrop-blur-md border;
	}
	/* Soft white canvas with subtle Strava-orange glow toward the bottom */
	.bg-hero-gradient {
		background-image: radial-gradient(
				1200px 600px at 85% -10%,
				hsl(var(--primary) / 0.1),
				transparent 60%
			),
			linear-gradient(
				to bottom,
				hsl(0 0% 100%) 0%,
				hsl(0 0% 100%) 60%,
				hsl(var(--primary) / 0.06) 100%
			);
	}

	/* Subtle radial halo behind the screenshot */
	.hero-halo {
		background: radial-gradient(
			60% 80% at 50% 0%,
			hsl(var(--primary) / 0.18),
			transparent 70%
		);
	}
}

/* Signature motion */
@keyframes floaty {
	0%,
	100% {
		transform: translateY(0) scale(1);
	}
	50% {
		transform: translateY(-6px) scale(1.02);
	}
}
.animate-orb {
	animation: floaty 10s ease-in-out infinite;
}
