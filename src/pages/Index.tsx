import { useEffect, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { PageSection } from "@/components/strava/layout";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";

const Index = () => {
  const navigate = useNavigate();

  const { data, isSuccess } = useQuery({
    queryKey: ['authStatus'],
    queryFn: async () => {
      const { data } = await axios.get('/api/auth-status');
      return data;
    },
    retry: false,
  });

  useEffect(() => {
    if (isSuccess && data?.loggedIn) {
      navigate('/dashboard', { replace: true });
    }
  }, [isSuccess, data, navigate]);

  useEffect(() => {
    // Only set the title if we are not about to redirect
    if (!isSuccess) {
      document.title = "Strava Profile Analyzer — Connect to analyze";
      const meta = document.querySelector('meta[name="description"]');
      if (meta) meta.setAttribute('content', 'Connect your Strava account to view your activities, trends, gear and stats.');
    }
  }, [isSuccess]);

  const heroLdJson = useMemo(() => ({
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: 'Strava Profile Analyzer',
    applicationCategory: 'SportsApplication',
    operatingSystem: 'Web',
    url: window.location.origin,
    description: 'Analyze Strava activities, trends, gear and stats in a clean dashboard.'
  }), []);

  // While checking auth, we can render a minimal page or a spinner.
  // For now, we'll render the normal page, and it will be replaced on redirect.
  // A brief flash of the homepage is acceptable for this use case.

  return (
    <div className="min-h-screen">
      <header className="relative min-h-screen overflow-hidden flex items-center isolate bg-hero-gradient">
        {/* Top navigation */}
        <nav className="absolute inset-x-0 top-0 z-10">
          <PageSection className="py-4">
            <div className="flex items-center justify-between">
              <a href="/" className="inline-flex items-center gap-2 text-foreground font-semibold tracking-tight">
                <span className="inline-block h-6 w-6 rounded-sm bg-gradient-primary" />
                <span>Strava Profile Analyzer</span>
              </a>
            </div>
          </PageSection>
        </nav>

        <PageSection className="w-full py-24">
          <div className="max-w-4xl mx-auto text-center">
            <p className="inline-flex items-center gap-2 rounded-full border border-foreground/10 bg-foreground/5 px-3 py-1 text-[11px] uppercase tracking-widest text-foreground/70">
              Analyze your training
            </p>
            <h1 className="mt-4 text-5xl md:text-6xl font-extrabold tracking-tight">
              Strava Profile Analyzer
            </h1>
            <p className="mt-4 text-muted-foreground max-w-2xl mx-auto text-base md:text-lg">
              Connect with Strava or paste an access token to generate your dashboard.
            </p>
            <div className="mt-8 flex flex-col items-center gap-4">
              <Button asChild size="xl" variant="hero" className="w-full sm:w-auto">
                <a href="/api/auth-login" aria-label="Connect with Strava">Connect with Strava</a>
              </Button>
            </div>
          </div>

          {/* Centered screenshot with halo */}
          <div className="relative max-w-5xl mx-auto mt-14">
            <div className="absolute -inset-x-20 -top-6 h-48 hero-halo blur-2xl" />
            <div className="relative rounded-2xl bg-gradient-primary p-[2px] shadow-glow">
              <div className="rounded-2xl overflow-hidden border bg-card">
                <img
                  src="/placeholder.svg"
                  alt="Dashboard preview"
                  className="block w-full h-auto"
                />
              </div>
            </div>
          </div>
        </PageSection>
      </header>

      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(heroLdJson) }} />
    </div>
  );
};

export default Index;
