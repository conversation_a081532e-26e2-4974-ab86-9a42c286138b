// A comment to trigger a fresh build
import { useEffect, useMemo, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { SummaryCards } from "@/components/strava/SummaryCards";
import { ActivityTrendsChart } from "@/components/strava/ActivityTrendsChart";
import { ActivityTypeDistribution } from "@/components/strava/ActivityTypeDistribution";
import { EquipmentList } from "@/components/strava/EquipmentList";
import { RecentActivitiesTable } from "@/components/strava/RecentActivitiesTable";
import { PageSection } from "@/components/strava/layout";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import axios from 'axios';
import { AthleteProfile } from '@/data/demo-strava.ts';

function isAthleteProfile(value: any): value is AthleteProfile {
  return (
    value &&
    typeof value === 'object' &&
    typeof value.id === 'string' &&
    typeof value.name === 'string' &&
    value.totals && typeof value.totals === 'object' &&
    Array.isArray(value.activities) &&
    Array.isArray(value.gear)
  );
}

const getStravaProfile = async (accessToken: string): Promise<AthleteProfile> => {
  // When using cookie-based auth via OAuth, the header is optional.
  // Keep header support for legacy/manual token mode.
  try {
    const apiBase = (import.meta as any).env?.VITE_API_BASE_URL || '';
    const url = `${apiBase}/api/strava`;
    const response = await axios.get(url, accessToken ? {
      headers: { Authorization: `Bearer ${accessToken}` },
    } : undefined);
    const data = response.data;
    if (!isAthleteProfile(data)) {
      throw new Error('Invalid response from server.');
    }
    return data;
  } catch (error: any) {
    const message = error?.response?.data?.error || error?.message || 'Failed to fetch Strava profile.';
    console.error('Error fetching Strava profile:', error);
    throw new Error(message);
  }
};

const Dashboard = () => {
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('strava_access_token');
    // Token may be absent when using cookie-based OAuth; allow fetch to proceed without it.
    if (token) setAccessToken(token);

    document.title = `Your Dashboard | Strava Profile Analyzer`;
    const meta = document.querySelector('meta[name="description"]');
    if (meta) meta.setAttribute('content', 'Your personal Strava dashboard with activities, trends, gear and stats.');
  }, [navigate]);

  const ldJson = useMemo(() => ({
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: `Your Strava Dashboard`,
    url: `${window.location.origin}/dashboard`,
    description: `Your personal Strava dashboard with activities, trends, gear and stats.`
  }), []);

  const {
    data: profile,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ['stravaProfile', accessToken ?? 'cookie'],
    queryFn: () => getStravaProfile(accessToken || ''),
    enabled: true,
    retry: false,
  });

  useEffect(() => {
    // If there is neither a local token nor a valid cookie session, redirect to /connect
    // We infer missing auth when the query finished and there is no profile and no loading
    if (!isLoading && !profile && !accessToken && !isError) {
      navigate('/connect');
    }
  }, [isLoading, profile, accessToken, isError, navigate]);

  useEffect(() => {
    // If the API explicitly says unauthorized, send the user to connect
    if (isError) {
      const message = (error as Error | undefined)?.message?.toLowerCase() || '';
      if (message.includes('unauthorized') || message.includes('connect your strava account')) {
        navigate('/connect', { replace: true });
      }
    }
  }, [isError, error, navigate]);

  const handleLogout = () => {
    localStorage.removeItem('strava_access_token');
    // Best-effort clear server cookies (non-blocking)
    fetch('/api/logout', { method: 'POST' }).finally(() => navigate('/'));
  };

  return (
    <div className="min-h-screen">
      <PageSection className="pt-8">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-semibold">Profile Dashboard</h1>
            {profile ? (
              <p className="text-muted-foreground">Welcome, {profile.name}!</p>
            ) : (
              <p className="text-muted-foreground">Your personal dashboard.</p>
            )}
          </div>
          <Button onClick={handleLogout} variant="soft">Logout</Button>
        </div>
      </PageSection>

      <main id="dashboard" className="space-y-10">
        {isLoading && (
          <PageSection>
            <p>Loading profile...</p>
          </PageSection>
        )}
        {isError && (
          <PageSection>
            <p className="text-red-500">Error: {(error as Error)?.message || 'An error occurred'}</p>
          </PageSection>
        )}
        {profile && (
          <>
            <PageSection>
              <SummaryCards totals={profile.totals} />
            </PageSection>

            <PageSection>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2"><ActivityTrendsChart activities={profile.activities} /></div>
                <div className="lg:col-span-1"><ActivityTypeDistribution activities={profile.activities} /></div>
              </div>
            </PageSection>

            <PageSection>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2"><RecentActivitiesTable activities={profile.activities} /></div>
                <div className="lg:col-span-1"><EquipmentList activities={profile.activities} gear={profile.gear} /></div>
              </div>
            </PageSection>
          </>
        )}
        {!isLoading && !profile && !isError && null}
      </main>

      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(ldJson) }} />
    </div>
  );
};

export default Dashboard;
