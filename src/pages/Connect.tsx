import { useEffect, useMemo } from "react";
import { PageSection } from "@/components/strava/layout";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const Connect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    document.title = "Connect to Strava — Strava Profile Analyzer";
    const meta = document.querySelector('meta[name="description"]');
    if (meta) meta.setAttribute('content', 'Connect with Strava or paste an access token to open your dashboard.');
  }, []);

  const ldJson = useMemo(() => ({
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name: 'Connect to Strava',
    url: `${window.location.origin}/connect`,
    description: 'Connect with Strava or paste an access token to open your dashboard.'
  }), []);

  return (
    <div className="min-h-screen bg-hero-gradient">
      <nav className="border-b">
        <PageSection className="py-4">
          <div className="flex items-center justify-between">
            <a href="/" className="inline-flex items-center gap-2 font-semibold tracking-tight">
              <span className="inline-block h-6 w-6 rounded-sm bg-gradient-primary" />
              <span>Strava Profile Analyzer</span>
            </a>
          </div>
        </PageSection>
      </nav>

      <main className="flex items-center justify-center">
        <PageSection className="w-full py-24">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Connect your account</h1>
            <p className="mt-3 text-muted-foreground">Authorize Strava to continue to your dashboard.</p>

            <div className="mt-8 flex flex-col items-center gap-4">
              <Button asChild size="xl" variant="hero" className="w-full sm:w-auto">
                <a href="/api/auth-login" aria-label="Connect with Strava">Connect with Strava</a>
              </Button>
            </div>
          </div>
        </PageSection>
      </main>

      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(ldJson) }} />
    </div>
  );
};

export default Connect;


